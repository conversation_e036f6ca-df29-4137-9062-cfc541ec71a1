package main

import "JiraTestPlan/request"

var api = "OMVBbsoek1OqdaaDW0faLQN6iJg4UMTFa55xeC"

func getExecutions(testPlan string) []string {
    endpoint := "api/2/search"

    // Example parameters for JIRA API search
    params := map[string]string{
        "jql":        "issue in testPlanTestExecutions('" + testPlan + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key",
    }

    request.MakeRequest(api, endpoint, params)
}

func main() {
    // get all test executions for the test plan
    issueKey := "DNS300-1391"
    executions := getExecutions(issueKey)
}
