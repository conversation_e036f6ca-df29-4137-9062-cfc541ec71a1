package main

import "JiraTestPlan/request"

var apiKey = "OMVBbsoek1OqdaaDW0faLQN6iJg4UMTFa55xeC"

func getExecutions(issueKey string) {
    endpoint := "api/2/search"

    // Example parameters for JIRA API search
    params := map[string]string{
        "jql":        "issue in testPlanTestExecutions('" + issueKey + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key",
    }

    // Now apiKey is accessible as a global variable
    request.MakeRequest(apiKey, endpoint, issueKey, params)
}

func main() {
    // Test Plan
    issueKey := "DNS300-1391"
    endpoint := "api/2/search"

    // Example parameters for JIRA API search
    params := map[string]string{
        "jql":        "issue in testPlanTestExecutions('" + issueKey + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key",
    }

    // Now using the global apiKey variable
    request.MakeRequest(apiKey, endpoint, issueKey, params)

    // You can also call the getExecutions function which uses the global apiKey
    // getExecutions(issueKey)
}
