package main

import "JiraTestPlan/request"

func main() {
    apiKey := "OMVBbsoek1OqdaaDW0faLQN6iJg4UMTFa55xeC"
    // Test Plan
    issueKey := "DNS300-1391"
    endpoint := "api/2/search"
    
    // Example parameters for JIRA API search
    params := map[string]string{
        "jql":        "issue in testPlanTestExecutions('" + issueKey + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key",
    }

    MakeRequest(apiKey, endpoint, params)
}
