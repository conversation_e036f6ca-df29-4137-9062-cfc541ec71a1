package main

import (
	"JiraTestPlan/request"
	"bytes"
	"fmt"
	"os"
)

var api = "OMVBbsoek1OqdaaDW0faLQN6iJg4UMTFa55xeC"

func getExecutions(testPlan string) []string {
    endpoint := "api/2/search"

    // Example parameters for JIRA API search
    params := map[string]string{
        "jql":        "issue in testPlanTestExecutions('" + testPlan + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key",
    }

    // Option 1: Use bytes.Buffer to capture response in memory
    var buffer bytes.Buffer
    err := request.MakeRequest(api, endpoint, params, &buffer)
    if err != nil {
        fmt.Printf("Error making request: %v\n", err)
        return nil
    }

    // Now you can process the response from buffer.String()
    responseData := buffer.String()
    fmt.Println("Response received:", len(responseData), "bytes")

    // TODO: Parse JSON response and extract keys into []string
    // For now, returning empty slice
    var keys []string
    return keys
}

func main() {
    // Example 1: Using bytes.Buffer (in-memory)
    issueKey := "DNS300-1391"
    executions := getExecutions(issueKey)
    fmt.Printf("Found %d executions\n", len(executions))

    // Example 2: Write directly to a file (for large responses)
    endpoint := "api/2/search"
    params := map[string]string{
        "jql":        "issue in testPlanTestExecutions('" + issueKey + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key,summary,status",
    }

    // Create a file to write the response
    file, err := os.Create("jira_response.json")
    if err != nil {
        fmt.Printf("Error creating file: %v\n", err)
        return
    }
    defer file.Close()

    // Write response directly to file (memory efficient for large responses)
    err = request.MakeRequest(api, endpoint, params, file)
    if err != nil {
        fmt.Printf("Error making request: %v\n", err)
        return
    }

    fmt.Println("Response written to jira_response.json")

    // Example 3: Write to stdout (for debugging)
    fmt.Println("\n--- Writing response to stdout ---")
    err = request.MakeRequest(api, endpoint, params, os.Stdout)
    if err != nil {
        fmt.Printf("Error making request: %v\n", err)
    }
}
