package main

import (
	"JiraTestPlan/request"
	"bytes"
	"fmt"
)

var api = "OMVBbsoek1OqdaaDW0faLQN6iJg4UMTFa55xeC"

func getExecutions(testPlan string) []string {
    endpoint := "api/2/search"

    // Example parameters for JIRA API search
    params := map[string]string{
        "jql":        "issue in testPlanTestExecutions('" + testPlan + "')",
        "startAt":    "0",
        "maxResults": "500",
        "fields":     "key",
    }

    // Option 1: Use bytes.Buffer to capture response in memory
    var buffer bytes.Buffer
    err := request.MakeRequest(api, endpoint, params, &buffer)
    if err != nil {
        fmt.Printf("Error making request: %v\n", err)
        return nil
    }

    // Now you can process the response from buffer.String()
    responseData := buffer.String()

    // TODO: Parse JSON response and extract keys into []string
    // For now, returning empty slice
    var keys []string
    return keys
}

func main() {
    issueKey := "DNS300-1391"
    executions := getExecutions(issueKey)
    fmt.Printf("Found %d executions\n", len(executions))
}
