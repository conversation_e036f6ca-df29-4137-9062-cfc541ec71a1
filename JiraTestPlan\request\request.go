package request

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
)

func MakeRequest(bearerToken string, endpoint string, params map[string]string) {
	baseURL := "https://jerry.dieboldnixdorf.com/rest/"

	// Parse the URL and add query parameters
	u, err := url.Parse(baseURL + endpoint)
	if err != nil {
		fmt.Println("Error parsing URL:", err)
		return
	}

	// Add query parameters
	q := u.Query()
	for key, value := range params {
		q.Add(key, value)
	}
	u.RawQuery = q.Encode()

	// Create a new request
	req, err := http.NewRequest("GET", u.String(), nil)
	if err != nil {
		fmt.Println("Error creating request:", err)
		return
	}

	// Add the Bearer token to the Authorization header
	req.Header.Add("Authorization", "Bearer "+bearerToken)

	// Create a new HTTP client and send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error making request:", err)
		return
	}
	defer resp.Body.Close()

	// Read the response body
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading response body:", err)
		return
	}

	// Print the response body
	fmt.Println(string(body))
}
