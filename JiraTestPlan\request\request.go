package request

import (
	"fmt"
	"net/http"
	"net/url"
	"io"
)

func MakeRequest(bearerToken string, endpoint string, queryParameters map[string]string, responseWriter io.Writer) error {
	jiraBaseURL := "https://jerry.dieboldnixdorf.com/rest/"

	// Parse the URL and add query parameters
	parsedURL, parseError := url.Parse(jiraBaseURL + endpoint)
	if parseError != nil {
		fmt.Println("Error parsing URL:", parseError)
		return parseError
	}

	// Add query parameters
	urlQuery := parsedURL.Query()
	for parameterKey, parameterValue := range queryParameters {
		urlQuery.Add(parameterKey, parameterValue)
	}
	parsedURL.RawQuery = urlQuery.Encode()

	// Create a new request
	httpRequest, requestCreationError := http.NewRequest("GET", parsedURL.String(), nil)
	if requestCreationError != nil {
		fmt.Println("Error creating request:", requestCreationError)
		return requestCreationError
	}

	// Add the Bearer token to the Authorization header
	httpRequest.Header.Add("Authorization", "Bearer " + bearerToken)

	// Create a new HTTP client and send the request
	httpClient := &http.Client{}
	httpResponse, requestExecutionError := httpClient.Do(httpRequest)
	if requestExecutionError != nil {
		fmt.Println("Error making request:", requestExecutionError)
		return requestExecutionError
	}
	defer httpResponse.Body.Close()

	// Write response to the writer
	_, copyError := io.Copy(responseWriter, httpResponse.Body)
	if copyError != nil {
		fmt.Println("Error writing response:", copyError)
		return copyError
	}

	return nil
}
