package main

import (
"fmt"
"io/ioutil"
"net/http"
"net/url"
)

func makeRequest(endpoint string) {
    bearerToken := "OMVBbsoek1OqdaaDW0faLQN6iJg4UMTFa55xeC"
    testPlanKey = "DNS300-1391"

    url := "https://jerry.dieboldnixdorf.com/rest/"

    // Create a new request
    req, err := http.NewRequest("GET", url+endpoint, nil)
    if err != nil {
        fmt.Println("Error creating request:", err)
        return
    }

    // Add the Bearer token to the Authorization header
    req.Header.Add("Authorization", "Bearer "+bearerToken)

    // Create a new HTTP client and send the request
    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        fmt.Println("Error making request:", err)
        return
    }
    defer resp.Body.Close()

    // Read the response body
    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        fmt.Println("Error reading response body:", err)
        return
    }

    // Print the response body
    fmt.Println(string(body))
}

func main() {
    endpoint := "api/2/search"
    makeRequest(endpoint)
}
